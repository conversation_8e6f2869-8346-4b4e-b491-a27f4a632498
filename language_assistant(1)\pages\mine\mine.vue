<template>
	<CustomNavbar title="我的" changeLang />
	<!-- 我的-->
	<view class="page-container">
		<!-- 自定义导航栏 -->
		<view>
			<!-- 用户信息 -->
			<view class="user-info">
				<template v-if="isLoggedIn">
					<image class="avatar" :src="userAvatar" />
					<view class="user-detail">
						<text class="phone">{{userNickname}}</text>
						<text class="user-id">{{userId}}</text>
					</view>
				</template>
				<template v-else>
					<image class="avatar" src="/static/icons/touxiang.svg" />
					<view class="user-detail" @click="openWeixinComponent">
						<text class="phone">点击登录</text>
					</view>
				</template>
			</view>
			<!-- 会员服务 -->
			<view class="vip-section">
				<view class="vip-header">
					<image class="vip-icon" src="/static/mine/VIP.png" />
					<text>开通会员享受无限制服务</text>
				</view>
				<!-- 会员套餐选择区域 -->
				<view class="huiyuan">
					<!-- 会员卡片循环渲染，点击可选中 -->
					<view v-for="(item,index) in hiuyuanList" :key="index" @click="selectVip(index)" :style="{
							width: '106.827px',
							/* 动态高度：1年会员选中时增高到150px，其他保持128.929px */
							height: (selectedVipIndex === index && item.name && item.name.includes('年')) ? '150px' : '128.929px',
							background: '#D8E6F5',
							borderRadius: '12rpx',
							/* 选中状态显示蓝色边框#006397，未选中为透明边框 */
							border: selectedVipIndex === index ? '2px solid #006397' : '2px solid transparent',
							cursor: 'pointer'
						}">
						<view class="huiyuan-title">
							<view class="huiyuan-title-name">
								{{item.name}}
							</view>
							<view class="huiyuan-title-desc">
								{{item.exceed_fee_desc}}
							</view>
							<!-- 价格显示区域 -->
							<view class="huiyuan-title-xuan">
								<!-- 当前价格：选中时显示橙红色#FF410A，未选中时显示黑色 -->
								<view class="huiyuan-title-xuan-flex">
									<view
										:style="{fontSize: '20rpx', marginTop: '4rpx', color: selectedVipIndex === index ? '#FF410A' : '#000'}">
										￥</view>
									<view
										:style="{fontSize: '14px', color: selectedVipIndex === index ? '#FF410A' : '#000', fontWeight: '600', margin: '0'}">
										{{item.price}}
									</view>
								</view>
								<!-- 原价：始终显示删除线样式 -->
								<view class="huiyuan-title-xuan-price">
									<view>￥</view>
									<view>{{item.original_price}}</view>
								</view>
							</view>
							<!-- 1年会员特殊提示：只有选中1年会员时才显示 -->
							<view class="huiyuan-title-pan"
								v-if="selectedVipIndex === index && item.name && item.name.includes('年')">
								现在购买可使用13个月
							</view>
						</view>
					</view>
				</view>
				<button class="vip-btn" @click="handleVipPurchase">
					{{ selectedVipIndex !== null ? `￥${hiuyuanList[selectedVipIndex]?.price} ` : '' }}确认协议并开通
				</button>
				<view class="vip-agree">
					<radio :checked="agree" @click="agree = !agree" color="#2a7cff" style="transform: scale(0.8);" />
					<text>开通前请阅读</text>
					<text class="agreement-link" @click="openMemberAgreement">《会员服务协议》</text>
					<text>和</text>
					<text class="agreement-link" @click="openPaymentAgreement">《扣费服务协议》</text>
				</view>


			</view>
			<!-- 菜单列表 -->
			<view class="menu-section">
				<view class="menu-item" @click="handleMenuClick('order')">
					<text class="menu-text">订单</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="handleMenuClick('language')">
					<text class="menu-text">语言设置</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="handleMenuClick('share')">
					<text class="menu-text">分享</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="handleMenuClick('agreement')">
					<text class="menu-text">用户协议</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="handleMenuClick('about')">
					<text class="menu-text">关于</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
		</view>
	</view>
	<CustumTabbar current="mine" />
	<!-- 引入微信组件 -->
	<WeixinComponent ref="weixinComponentRef" @close="handleWeixinClose" />
	<!-- 引入协议组件 -->
	<AgreementComponent ref="agreementComponentRef" @agree-read="handleAgreementRead" />
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';

	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		userinfo,
		MemberList
	} from '@/request/api.js'
	import WeixinComponent from '@/components/weixin/index.vue';
	import CustumTabbar from '@/components/custum-tabbar/index.vue'
	import AgreementComponent from '@/components/agreement/index.vue'


	const weixinComponentRef = ref(null);
	const agreementComponentRef = ref(null);


	// ==================== 会员相关功能 ====================
	// 获取会员列表数据
	const hiuyuanList = ref([]);
	// 当前选中的会员索引（默认选中第一个会员）
	const selectedVipIndex = ref(0);
	// 用户是否同意协议（购买前必须勾选）
	const agree = ref(false);

	// 从后端获取会员套餐列表
	const shoplist = () => {
		MemberList().then((res) => {
			hiuyuanList.value = res.list;
			console.log('获取的会员列表', res.list)
		})
	}

	// 选择会员套餐功能
	// @param {number} index - 选中的会员在数组中的索引
	const selectVip = (index) => {
		selectedVipIndex.value = index;
		console.log('选中会员:', hiuyuanList.value[index]);
	}

	// 处理会员购买逻辑
	const handleVipPurchase = () => {
		// 第一步：检查是否选择了会员套餐
		if (selectedVipIndex.value === null || hiuyuanList.value.length === 0) {
			uni.showToast({
				title: '请先选择会员套餐',
				icon: 'none'
			});
			return;
		}

		// 第二步：检查是否同意服务协议
		if (!agree.value) {
			uni.showToast({
				title: '请先阅读并同意服务协议',
				icon: 'none'
			});
			return;
		}

		// 第三步：获取选中的会员信息并处理购买
		const selectedVip = hiuyuanList.value[selectedVipIndex.value];
		console.log('准备购买会员:', selectedVip);

		// TODO: 这里可以添加实际的购买API调用逻辑
		uni.showToast({
			title: `准备购买${selectedVip.name}`,
			icon: 'success'
		});
	}

	// 用户信息状态
	const userAvatar = ref('/static/mine/avatar.jpg');
	const userNickname = ref('点击登录');
	const userId = ref('');
	const isLoggedIn = ref(false);

	// 优化后的 userlist 方法
	const userlist = async () => {

		const res = await userinfo();
		if (res && res.code === 200 && res.data) {
			const data = res.data;
			userAvatar.value = data.avatar || '/static/mine/avatar.jpg';
			userNickname.value = data.nickname || '未设置昵称';
			userId.value = data.id ? `ID:${data.id}` : '';
			isLoggedIn.value = true;
		} else {
			isLoggedIn.value = false;
			userAvatar.value = '/static/mine/avatar.jpg';
			userNickname.value = '点击登录';
			userId.value = '';
		}
	};

	// 页面显示时获取用户信息
	onShow(() => {
		userlist();
		shoplist();
	});

	// 页面挂载时也可获取用户信息（可选）
	onMounted(() => {
		userlist();
	});

	// 打开微信组件
	const openWeixinComponent = () => {
		if (weixinComponentRef.value) {
			weixinComponentRef.value.open();
		}
	};

	// 处理微信组件关闭事件，登录后刷新用户信息
	const handleWeixinClose = (loggedIn) => {
		console.log('微信组件已关闭', loggedIn ? '已登录' : '');
		if (loggedIn) {
			userlist();
		}
	};

	// 打开会员服务协议
	const openMemberAgreement = () => {
		if (agreementComponentRef.value) {
			agreementComponentRef.value.openAgreement('member');
		}
	};

	// 打开扣费服务协议
	const openPaymentAgreement = () => {
		if (agreementComponentRef.value) {
			agreementComponentRef.value.openAgreement('payment');
		}
	};

	// 处理用户阅读协议事件
	const handleAgreementRead = () => {
		agree.value = true;
		console.log('用户已阅读协议，自动勾选同意按钮');
	};

	// 处理菜单点击事件
	const handleMenuClick = (type) => {
		switch (type) {
			case 'order':
				console.log('点击订单');
				// TODO: 跳转到订单页面
				uni.showToast({
					title: '订单功能开发中',
					icon: 'none'
				});
				break;
			case 'language':
				console.log('点击语言设置');
				// TODO: 跳转到语言设置页面
				uni.showToast({
					title: '语言设置功能开发中',
					icon: 'none'
				});
				break;
			case 'share':
				console.log('点击分享');
				// TODO: 调用分享功能
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
				break;
			case 'agreement':
				console.log('点击用户协议');
				// TODO: 跳转到用户协议页面
				uni.showToast({
					title: '用户协议功能开发中',
					icon: 'none'
				});
				break;
			case 'about':
				console.log('点击关于');
				// TODO: 跳转到关于页面
				uni.showToast({
					title: '关于功能开发中',
					icon: 'none'
				});
				break;
			default:
				break;
		}
	};
</script>

<style lang="scss" scoped>
	.page-container {
		// padding: 0 32rpx;
		margin: 12rpx 16rpx;
		// height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;
	}

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		padding: 80rpx 32rpx 20rpx;
		display: flex;
		align-items: center;
		background-color: #f5f7fa;
		z-index: 100;
	}

	.lang-switch {
		font-size: 28rpx;
		padding: 6rpx 16rpx;
		border-radius: 32rpx;
		background-color: #2a7cff;
		color: white;
	}

	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 36rpx;
	}

	.user-info {
		display: flex;
		align-items: center;
		padding: 32rpx 24rpx 0 24rpx;
		position: relative;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
	}

	.login-tip {
		position: absolute;
		right: 24rpx;
		font-size: 24rpx;
		color: #2a7cff;
	}

	.user-detail {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
	}

	.phone {
		font-size: 32rpx;
		font-weight: bold;
	}

	.user-id {
		font-size: 24rpx;
		color: #888;
		margin-top: 8rpx;
	}

	.vip-section {
		background: #eaf0f7;
		// margin: 24rpx;
		border-radius: 20rpx;
		margin-top: 20rpx;
		padding-bottom: 40rpx;
		// padding: 24rpx;
	}

	.vip-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.vip-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 8rpx;
	}

	.vip-btn {
		width: 86%;
		background: #ff4d00;
		color: #fff;
		border-radius: 30rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		margin-bottom: 40rpx;
	}

	.vip-agree {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 22rpx;
		color: #888;
		flex-wrap: wrap;
		gap: 4rpx;
	}

	.agreement-link {
		color: #2a7cff !important;
		// text-decoration: underline;
		cursor: pointer;
	}

	.huiyuan {
		display: flex;
		gap: 20rpx;
		margin-bottom: 16rpx;
		justify-content: center;
		align-items: center;

		&-title {
			margin: 8rpx 8rpx;

			&-name {
				color: #000;
				font-size: 18px;
				font-weight: 600;
				padding-top: 30rpx;
				margin-left: 10rpx;
			}

			&-desc {
				color: #292D32;
				font-size: 20rpx;
				margin-top: 10rpx;
				margin-left: 10rpx;
			}

			&-xuan {
				display: flex;
				justify-content: space-between;
				margin-top: 70rpx;

				&-flex {
					display: flex;
				}

				&-price {
					font-size: 20rpx;
					display: flex;
					margin-top: 4rpx;
					text-decoration: line-through;
				}
			}

			&-pan {
				text-align: center;
				margin-top: 8rpx;
				font-size: 18rpx;
				color: #FF6B35;
				font-weight: 500;
			}
		}
	}

	.menu-section {
		background: #eaf0f7;
		border-radius: 20rpx;
		margin-top: 40rpx;
		padding: 0;
		overflow: hidden;
	}

	.menu-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 24rpx;
		position: relative;
		transition: background-color 0.2s ease;

		&:not(:last-child)::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 1rpx;
			background: linear-gradient(90deg, rgba(236, 229, 242, 0.20) 0%, #89848C 50.5%, rgba(246, 242, 249, 0.20) 100%);
		}

		&:active {
			background-color: #f8f9fa;
		}
	}

	.menu-text {
		font-size: 28rpx;
		color: #595859;
		font-weight: 400;
	}

	.menu-arrow {
		font-size: 32rpx;
		color: #999;
		font-weight: 300;
	}
</style>