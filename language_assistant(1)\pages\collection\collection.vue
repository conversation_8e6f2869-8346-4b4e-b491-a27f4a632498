<template>
	<CustomNavbar title="我的收藏" changeLang />
	<!-- 收藏页面 -->
	<view>
		<view class="container">

			<!-- 标签栏 -->
			<view class="tabs-container">
				<view class="tabs-wrapper">
					<view v-for="(tab, index) in tabs" :key="index"
						:class="['tab-item', { active: activeTab === index }]" @click="switchTab(index)">
						<text class="tabs-wrapper-flex">{{ tab.name }}</text>
					</view>
				</view>
			</view>

			<!-- 内容区域 -->
			<view class="content-container">
				<!-- 判断是否有数据 -->
				<view v-if="currentTabData.length > 0">
					<!-- 循环生成内容列表 -->
					<view class="content-list">
						<view v-for="item in currentTabData" :key="item.id" class="content-card"
							@click="goToDetailPage(item)">
							<view class="card-content">
								<image class="card-cover" :src="item.cover_image" mode="aspectFill"></image>
								<view class="card-info">
									<view class="card-title">{{ item.title }}</view>
									<view class="card-subtitle">{{ item.subtitle }}</view>
									<view class="card-date">{{ item.date }}</view>
									<view class="card-rating">
										<view class="rating-stars">
											<text class="rating-score">{{ item.rating }}</text>
											<view class="stars">
												<text v-for="star in 5" :key="star"
													:class="['star', { filled: star <= Math.floor(item.rating) }]">★</text>
											</view>
										</view>
										<view class="rating-count">({{ item.reviewCount }})</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else class="no-data">
					<text>{{ getNoDataMessage() }}</text>
				</view>
			</view>

			<!-- 加载更多提示 -->
			<view class="load-more" v-if="currentTabData.length > 0">
				<text v-if="isLoading">加载中...</text>
				<text v-else-if="!hasMore">没有更多数据了</text>
			</view>
		</view>
	</view>
	<CustumTabbar current="collection" />
</template>

<script setup>
	import {
		onMounted,
		ref
	} from 'vue';
	import CustumTabbar from '@/components/custum-tabbar/index.vue'
	import {
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app'

	// 标签数据
	const tabs = ref([{
			name: '收藏对话',
			type: 'dialogue'
		},
		{
			name: '收藏课件',
			type: 'courseware'
		},
	]);

	const activeTab = ref(0);

	// 模拟数据
	const dialogueData = ref([{
			id: 1,
			title: '在火车站怎么买票对话学习',
			subtitle: '音频文字对话',
			date: '2025年1月20日 19：23',
			rating: 4.5,
			reviewCount: 24,
			cover_image: '/static/mine/avatar.jpg'
		},
		{
			id: 2,
			title: '在火车站怎么买票对话学习',
			subtitle: '音频文字对话',
			date: '2025年1月20日 19：23',
			rating: 4.5,
			reviewCount: 24,
			cover_image: '/static/mine/avatar.jpg'
		},
		{
			id: 3,
			title: '在火车站怎么买票对话学习',
			subtitle: '音频文字对话',
			date: '2025年1月20日 19：23',
			rating: 4.5,
			reviewCount: 24,
			cover_image: '/static/mine/avatar.jpg'
		}
	]);

	const coursewareData = ref([{
		id: 4,
		title: '在火车站怎么买票对话学习',
		subtitle: '音频文字对话',
		date: '2025年1月20日 19：23',
		rating: 4.5,
		reviewCount: 24,
		cover_image: '/static/mine/avatar.jpg'
	}]);

	const isLoading = ref(false);
	const hasMore = ref(false);

	// 计算当前标签的数据
	const currentTabData = ref([]);

	// 切换标签
	const switchTab = (index) => {
		activeTab.value = index;
		updateCurrentTabData();
	};

	// 更新当前标签数据
	const updateCurrentTabData = () => {
		const currentTab = tabs.value[activeTab.value];
		if (currentTab.type === 'dialogue' || currentTab.type === 'dialogue2') {
			currentTabData.value = dialogueData.value;
		} else {
			currentTabData.value = coursewareData.value;
		}
	};

	// 获取无数据时的提示信息
	const getNoDataMessage = () => {
		const currentTab = tabs.value[activeTab.value];
		if (currentTab.type === 'dialogue' || currentTab.type === 'dialogue2') {
			return '暂无收藏对话';
		} else {
			return '暂无收藏课件';
		}
	};

	// 页面触底加载更多
	onReachBottom(() => {
		console.log('触底加载更多');
		if (hasMore.value && !isLoading.value) {
			// TODO: 实现加载更多逻辑
		}
	});

	// 跳转到详情页
	const goToDetailPage = (item) => {
		console.log('点击：', item);
		const currentTab = tabs.value[activeTab.value];
		if (currentTab.type === 'dialogue' || currentTab.type === 'dialogue2') {
			// 跳转到对话详情页
			uni.navigateTo({
				url: `/pages/dialogue/detail?id=${item.id}`
			});
		} else {
			// 跳转到课件详情页
			uni.navigateTo({
				url: `/pages/courseware/detail?id=${item.id}`
			});
		}
	};

	onMounted(() => {
		updateCurrentTabData();
	})

	onShow(() => {
		updateCurrentTabData();
	})
</script>


<style lang="scss" scoped>
	.container {
		padding: 0 32rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f7fa;
	}

	// 标签栏样式
	.tabs-container {
		margin-top: 20rpx;
		padding: 0 20rpx;
	}

	.tabs-wrapper {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;

		&-flex {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.tab-item {
		padding: 16rpx 32rpx;
		border-radius: 40rpx;
		background-color: #e5e5e5;
		font-size: 28rpx;
		color: #666;
		transition: all 0.3s ease;
		white-space: nowrap;

		&.active {
			background-color: #007acc;
			color: #fff;
		}
	}

	// 内容区域
	.content-container {
		flex: 1;
		padding: 20rpx;
		overflow-y: auto;
	}

	.content-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.content-card {
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.card-content {
		display: flex;
		gap: 20rpx;
	}

	.card-cover {
		width: 120rpx;
		height: 120rpx;
		border-radius: 12rpx;
		background: #eaeaea;
		flex-shrink: 0;
	}

	.card-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		line-height: 1.4;
		margin-bottom: 8rpx;
	}

	.card-subtitle {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	.card-date {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 12rpx;
	}

	.card-rating {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.rating-stars {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.rating-score {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.stars {
		display: flex;
		gap: 2rpx;
	}

	.star {
		font-size: 24rpx;
		color: #ddd;

		&.filled {
			color: #ffa500;
		}
	}

	.rating-count {
		font-size: 24rpx;
		color: #666;
	}

	.no-data {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		font-size: 28rpx;
		color: #666;
	}

	.load-more {
		text-align: center;
		padding: 20rpx 0;
		font-size: 24rpx;
		color: #999;
	}
</style>