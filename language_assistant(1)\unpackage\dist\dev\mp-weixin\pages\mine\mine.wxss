/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-7c2ebfa5 {
  margin: 12rpx 16rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}
.custom-nav.data-v-7c2ebfa5 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 80rpx 32rpx 20rpx;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  z-index: 100;
}
.lang-switch.data-v-7c2ebfa5 {
  font-size: 28rpx;
  padding: 6rpx 16rpx;
  border-radius: 32rpx;
  background-color: #2a7cff;
  color: white;
}
.nav-title.data-v-7c2ebfa5 {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
}
.user-info.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 0 24rpx;
  position: relative;
}
.avatar.data-v-7c2ebfa5 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.login-tip.data-v-7c2ebfa5 {
  position: absolute;
  right: 24rpx;
  font-size: 24rpx;
  color: #2a7cff;
}
.user-detail.data-v-7c2ebfa5 {
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
}
.phone.data-v-7c2ebfa5 {
  font-size: 32rpx;
  font-weight: bold;
}
.user-id.data-v-7c2ebfa5 {
  font-size: 24rpx;
  color: #888;
  margin-top: 8rpx;
}
.vip-section.data-v-7c2ebfa5 {
  background: #eaf0f7;
  border-radius: 20rpx;
  margin-top: 20rpx;
  padding-bottom: 40rpx;
}
.vip-header.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.vip-icon.data-v-7c2ebfa5 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.vip-btn.data-v-7c2ebfa5 {
  width: 86%;
  background: #ff4d00;
  color: #fff;
  border-radius: 30rpx;
  font-size: 32rpx;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}
.vip-agree.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #888;
  flex-wrap: wrap;
  gap: 4rpx;
}
.agreement-link.data-v-7c2ebfa5 {
  color: #2a7cff !important;
  cursor: pointer;
}
.huiyuan.data-v-7c2ebfa5 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
  justify-content: center;
  align-items: center;
}
.huiyuan-title.data-v-7c2ebfa5 {
  margin: 8rpx 8rpx;
}
.huiyuan-title-name.data-v-7c2ebfa5 {
  color: #000;
  font-size: 18px;
  font-weight: 600;
  padding-top: 30rpx;
  margin-left: 10rpx;
}
.huiyuan-title-desc.data-v-7c2ebfa5 {
  color: #292D32;
  font-size: 20rpx;
  margin-top: 10rpx;
  margin-left: 10rpx;
}
.huiyuan-title-xuan.data-v-7c2ebfa5 {
  display: flex;
  justify-content: space-between;
  margin-top: 70rpx;
}
.huiyuan-title-xuan-flex.data-v-7c2ebfa5 {
  display: flex;
}
.huiyuan-title-xuan-price.data-v-7c2ebfa5 {
  font-size: 20rpx;
  display: flex;
  margin-top: 4rpx;
  text-decoration: line-through;
}
.huiyuan-title-pan.data-v-7c2ebfa5 {
  text-align: center;
  margin-top: 8rpx;
  font-size: 18rpx;
  color: #FF6B35;
  font-weight: 500;
}
.menu-section.data-v-7c2ebfa5 {
  background: #eaf0f7;
  border-radius: 20rpx;
  margin-top: 20rpx;
  padding: 0;
  overflow: hidden;
  border: 1px solid red;
}
.menu-item.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}
.menu-item.data-v-7c2ebfa5:last-child {
  border-bottom: none;
}
.menu-item.data-v-7c2ebfa5:active {
  background-color: #f8f9fa;
}
.menu-text.data-v-7c2ebfa5 {
  font-size: 32rpx;
  color: #333;
  font-weight: 400;
}
.menu-arrow.data-v-7c2ebfa5 {
  font-size: 32rpx;
  color: #999;
  font-weight: 300;
}